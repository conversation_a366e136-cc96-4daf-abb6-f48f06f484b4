import { AppState, AppStateStatus, Platform } from 'react-native';
import { notificationService } from './notificationService';

export type FocusContext = 'focus' | 'break' | 'normal';

export interface AppSwitchEvent {
  timestamp: Date;
  fromApp: string;
  toApp: string;
  context: FocusContext;
  duration: number; // Time spent in the previous app
}

export interface DistractionAttempt {
  timestamp: Date;
  appName: string;
  packageName: string;
  context: FocusContext;
  blocked: boolean;
  duration: number; // How long they tried to use the app
}

class MobileAppMonitoringService {
  private isMonitoring = false;
  private currentContext: FocusContext = 'normal';
  private appStateSubscription: any = null;
  private currentAppState: AppStateStatus = 'active';
  private lastAppSwitchTime = Date.now();
  private focusAppPackage = 'com.isotope.ai.timemanagement'; // Our app's package name
  private blockedApps: Set<string> = new Set();
  private strictMode = false;

  // Event listeners
  private onDistractionAttemptListeners: ((attempt: DistractionAttempt) => void)[] = [];
  private onAppSwitchListeners: ((event: AppSwitchEvent) => void)[] = [];

  constructor() {
    this.setupAppStateListener();
  }

  private setupAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    const now = Date.now();
    const duration = now - this.lastAppSwitchTime;

    if (this.isMonitoring && this.currentContext !== 'normal') {
      if (this.currentAppState === 'active' && nextAppState === 'background') {
        // User switched away from our app
        this.handleAppSwitch('focus_app', 'unknown_app', duration);
      } else if (this.currentAppState === 'background' && nextAppState === 'active') {
        // User returned to our app
        this.handleAppReturn(duration);
      }
    }

    this.currentAppState = nextAppState;
    this.lastAppSwitchTime = now;
  };

  private handleAppSwitch(fromApp: string, toApp: string, duration: number) {
    const event: AppSwitchEvent = {
      timestamp: new Date(),
      fromApp,
      toApp,
      context: this.currentContext,
      duration,
    };

    // Notify listeners
    this.onAppSwitchListeners.forEach(listener => listener(event));

    // If switching during focus time, record as potential distraction
    if (this.currentContext === 'focus') {
      this.recordPotentialDistraction(toApp, duration);
    }
  }

  private handleAppReturn(awayDuration: number) {
    if (this.currentContext === 'focus' && awayDuration > 5000) { // More than 5 seconds away
      // Show encouragement message
      notificationService.showFocusEncouragement();
    }
  }

  private recordPotentialDistraction(appName: string, duration: number) {
    // Simulate checking if the app is blocked
    const isBlocked = this.isAppBlocked(appName);
    
    const attempt: DistractionAttempt = {
      timestamp: new Date(),
      appName: appName === 'unknown_app' ? 'Unknown App' : appName,
      packageName: appName,
      context: this.currentContext,
      blocked: isBlocked,
      duration,
    };

    // Notify listeners
    this.onDistractionAttemptListeners.forEach(listener => listener(attempt));

    // Show blocking notification if app is blocked
    if (isBlocked) {
      notificationService.showDistractionBlocked(attempt.appName);
    }
  }

  private isAppBlocked(packageName: string): boolean {
    if (this.currentContext === 'normal') return false;
    if (this.currentContext === 'break' && !this.strictMode) return false;
    
    return this.blockedApps.has(packageName) || packageName === 'unknown_app';
  }

  // Public methods
  startMonitoring(context: FocusContext = 'focus') {
    this.isMonitoring = true;
    this.currentContext = context;
    this.lastAppSwitchTime = Date.now();
    
    console.log(`Started app monitoring in ${context} context`);
  }

  stopMonitoring() {
    this.isMonitoring = false;
    this.currentContext = 'normal';
    
    console.log('Stopped app monitoring');
  }

  setContext(context: FocusContext) {
    this.currentContext = context;
    
    if (context === 'normal') {
      this.stopMonitoring();
    } else if (!this.isMonitoring) {
      this.startMonitoring(context);
    }
  }

  updateBlockedApps(apps: string[]) {
    this.blockedApps = new Set(apps);
  }

  setStrictMode(enabled: boolean) {
    this.strictMode = enabled;
  }

  // Simulation methods for testing
  simulateAppSwitch(appName: string, packageName: string) {
    if (!this.isMonitoring) return;

    const duration = Math.random() * 10000 + 1000; // Random duration between 1-11 seconds
    this.handleAppSwitch('focus_app', packageName, duration);
    
    // Simulate returning to focus app after a short time
    setTimeout(() => {
      this.handleAppReturn(duration);
    }, duration);
  }

  simulateDistractionAttempt(appName: string, packageName: string) {
    if (!this.isMonitoring) return;

    const duration = Math.random() * 5000 + 500; // Random duration between 0.5-5.5 seconds
    this.recordPotentialDistraction(packageName, duration);
  }

  // Event listeners
  onDistractionAttempt(listener: (attempt: DistractionAttempt) => void) {
    this.onDistractionAttemptListeners.push(listener);
    
    return () => {
      const index = this.onDistractionAttemptListeners.indexOf(listener);
      if (index > -1) {
        this.onDistractionAttemptListeners.splice(index, 1);
      }
    };
  }

  onAppSwitch(listener: (event: AppSwitchEvent) => void) {
    this.onAppSwitchListeners.push(listener);
    
    return () => {
      const index = this.onAppSwitchListeners.indexOf(listener);
      if (index > -1) {
        this.onAppSwitchListeners.splice(index, 1);
      }
    };
  }

  // Cleanup
  destroy() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.onDistractionAttemptListeners = [];
    this.onAppSwitchListeners = [];
  }

  // Getters
  get isActive() {
    return this.isMonitoring;
  }

  get context() {
    return this.currentContext;
  }
}

export const mobileAppMonitoringService = new MobileAppMonitoringService();
