import { Platform, Alert } from 'react-native';
import * as Haptics from 'expo-haptics';

export interface NotificationOptions {
  title: string;
  message: string;
  type?: 'success' | 'warning' | 'error' | 'info';
  duration?: number;
  haptic?: boolean;
}

class NotificationService {
  private isHapticsAvailable = Platform.OS !== 'web';

  async showNotification(options: NotificationOptions): Promise<void> {
    const { title, message, type = 'info', haptic = true } = options;

    // Trigger haptic feedback if available and requested
    if (haptic && this.isHapticsAvailable) {
      try {
        switch (type) {
          case 'success':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'warning':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case 'error':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          default:
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
      } catch (error) {
        console.warn('Haptic feedback failed:', error);
      }
    }

    // Show platform-appropriate notification
    if (Platform.OS === 'web') {
      // For web, use browser notifications if available
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
          body: message,
          icon: '/favicon.png',
        });
      } else {
        // Fallback to alert
        Alert.alert(title, message);
      }
    } else {
      // For mobile, use Alert for now (can be enhanced with push notifications)
      Alert.alert(title, message);
    }
  }

  async showSuccessNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Success',
      message,
      type: 'success',
    });
  }

  async showWarningNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Warning',
      message,
      type: 'warning',
    });
  }

  async showErrorNotification(message: string): Promise<void> {
    await this.showNotification({
      title: 'Error',
      message,
      type: 'error',
    });
  }

  async showFocusCompleteNotification(sessionDuration: number): Promise<void> {
    const minutes = Math.floor(sessionDuration / 60);
    await this.showNotification({
      title: '🎯 Focus Session Complete!',
      message: `Great job! You focused for ${minutes} minutes. Time for a well-deserved break.`,
      type: 'success',
    });
  }

  async showBreakReminder(): Promise<void> {
    await this.showNotification({
      title: '☕ Break Time!',
      message: 'Take a short break to recharge. You\'ve earned it!',
      type: 'info',
    });
  }

  async showDistractionBlocked(appName: string): Promise<void> {
    await this.showNotification({
      title: '🚫 App Blocked',
      message: `${appName} is blocked during focus time. Stay focused!`,
      type: 'warning',
      haptic: true,
    });
  }

  async showFocusEncouragement(): Promise<void> {
    await this.showNotification({
      title: '💪 Stay Focused!',
      message: 'You\'re doing great! Keep up the momentum.',
      type: 'info',
      haptic: true,
    });
  }

  async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'web') {
      if ('Notification' in window) {
        const permission = await Notification.requestPermission();
        return permission === 'granted';
      }
    }
    // For mobile, permissions are handled by the system
    return true;
  }

  async triggerHaptic(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'medium'): Promise<void> {
    if (!this.isHapticsAvailable) return;

    try {
      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }
}

export const notificationService = new NotificationService();
