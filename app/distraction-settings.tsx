import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput,
  FlatList,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  ArrowLeft,
  Shield,
  Smartphone,
  Globe,
  Plus,
  Trash2,
  Settings,
  Clock,
  Zap,
  Search,
  Check,
  X,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { installedAppsService, InstalledApp } from '@/services/installedAppsService';
import { mobileAppMonitoringService } from '@/services/mobileAppMonitoringService';

export default function DistractionSettingsScreen() {
  const router = useRouter();
  const {
    blockedApps,
    blockedWebsites,
    blockingSettings,
    updateBlockingSettings,
    updateBlockedApp,
    updateBlockedWebsite,
    addBlockedApp,
    addBlockedWebsite,
    removeBlockedApp,
    removeBlockedWebsite,
    isLoading,
  } = useDistractionBlocking();

  const [showAddApp, setShowAddApp] = useState(false);
  const [showAddWebsite, setShowAddWebsite] = useState(false);
  const [newAppName, setNewAppName] = useState('');
  const [newWebsiteName, setNewWebsiteName] = useState('');
  const [newWebsiteUrl, setNewWebsiteUrl] = useState('');
  const [appSearchQuery, setAppSearchQuery] = useState('');
  const [suggestedApps, setSuggestedApps] = useState<InstalledApp[]>([]);
  const [showAppSuggestions, setShowAppSuggestions] = useState(false);
  const [installedApps, setInstalledApps] = useState<InstalledApp[]>([]);
  const [isLoadingApps, setIsLoadingApps] = useState(false);
  const [appLoadError, setAppLoadError] = useState<string | null>(null);

  // Load installed apps and suggestions on component mount
  useEffect(() => {
    loadInstalledApps();
    loadSuggestedApps();
  }, []);

  const loadSuggestedApps = () => {
    try {
      const suggestions = mobileAppMonitoringService.getSuggestedBlockedApps();
      setSuggestedApps(suggestions.map(app => ({
        name: app.name,
        packageName: app.packageName,
        icon: null, // We don't have icons for suggested apps
        category: app.category,
      })));
    } catch (error) {
      console.error('Error loading suggested apps:', error);
    }
  };

  // Load app suggestions when search query changes
  useEffect(() => {
    if (appSearchQuery.length > 0) {
      searchApps(appSearchQuery);
    } else {
      setSuggestedApps([]);
      setShowAppSuggestions(false);
    }
  }, [appSearchQuery]);

  const loadInstalledApps = async () => {
    setIsLoadingApps(true);
    setAppLoadError(null);
    try {
      const apps = await installedAppsService.getInstalledApps();
      setInstalledApps(apps);
    } catch (error) {
      console.warn('Failed to load installed apps:', error);
      setAppLoadError('Failed to load installed apps. Using common apps instead.');
    } finally {
      setIsLoadingApps(false);
    }
  };

  const searchApps = async (query: string) => {
    try {
      const results = await installedAppsService.searchApps(query);
      setSuggestedApps(results.slice(0, 10)); // Limit to 10 suggestions
      setShowAppSuggestions(results.length > 0);
    } catch (error) {
      console.warn('Failed to search apps:', error);
      setSuggestedApps([]);
      setShowAppSuggestions(false);
    }
  };

  const getPopularAppSuggestions = async () => {
    try {
      const socialApps = await installedAppsService.getAppSuggestionsByCategory('social');
      const entertainmentApps = await installedAppsService.getAppSuggestionsByCategory('entertainment');
      const combined = [...socialApps.slice(0, 4), ...entertainmentApps.slice(0, 4)];
      setSuggestedApps(combined);
      setShowAppSuggestions(combined.length > 0);
    } catch (error) {
      console.warn('Failed to get popular apps:', error);
      // Fallback to first few installed apps
      const fallback = installedApps.slice(0, 8);
      setSuggestedApps(fallback);
      setShowAppSuggestions(fallback.length > 0);
    }
  };

  const handleToggleBlocking = async (enabled: boolean) => {
    try {
      await updateBlockingSettings({ isEnabled: enabled });
    } catch (error) {
      Alert.alert('Error', 'Failed to update blocking settings');
    }
  };

  const handleToggleStrictMode = async (enabled: boolean) => {
    try {
      await updateBlockingSettings({ strictMode: enabled });
    } catch (error) {
      Alert.alert('Error', 'Failed to update strict mode');
    }
  };

  const handleToggleAppBlocking = async (appId: string, isBlocked: boolean) => {
    try {
      await updateBlockedApp(appId, { isBlocked });
    } catch (error) {
      Alert.alert('Error', 'Failed to update app blocking');
    }
  };

  const handleToggleWebsiteBlocking = async (websiteId: string, isBlocked: boolean) => {
    try {
      await updateBlockedWebsite(websiteId, { isBlocked });
    } catch (error) {
      Alert.alert('Error', 'Failed to update website blocking');
    }
  };

  const handleAddApp = async (suggestedApp?: InstalledApp) => {
    const appToAdd = suggestedApp || {
      appName: newAppName.trim(),
      packageName: `com.${newAppName.toLowerCase().replace(/\s+/g, '')}.app`,
      icon: '📱'
    };

    if (!appToAdd.appName) {
      Alert.alert('Error', 'Please enter an app name or select from suggestions');
      return;
    }

    try {
      await addBlockedApp({
        name: appToAdd.appName,
        packageName: appToAdd.packageName,
        icon: appToAdd.icon || '📱',
        category: 'other',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      });
      setNewAppName('');
      setAppSearchQuery('');
      setSuggestedApps([]);
      setShowAppSuggestions(false);
      setShowAddApp(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to add app');
    }
  };

  const handleAddWebsite = async () => {
    if (!newWebsiteName.trim() || !newWebsiteUrl.trim()) {
      Alert.alert('Error', 'Please enter both website name and URL');
      return;
    }

    try {
      const url = newWebsiteUrl.startsWith('http') ? newWebsiteUrl : `https://${newWebsiteUrl}`;
      const domain = new URL(url).hostname.replace('www.', '');
      
      await addBlockedWebsite({
        name: newWebsiteName.trim(),
        url,
        domain,
        category: 'other',
        isBlocked: true,
        blockDuringFocus: true,
        blockDuringBreaks: false,
      });
      setNewWebsiteName('');
      setNewWebsiteUrl('');
      setShowAddWebsite(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to add website. Please check the URL format.');
    }
  };

  const handleRemoveApp = async (appId: string, appName: string) => {
    Alert.alert(
      'Remove App',
      `Are you sure you want to remove ${appName} from the blocked list?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeBlockedApp(appId);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove app');
            }
          },
        },
      ]
    );
  };

  const handleRemoveWebsite = async (websiteId: string, websiteName: string) => {
    Alert.alert(
      'Remove Website',
      `Are you sure you want to remove ${websiteName} from the blocked list?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeBlockedWebsite(websiteId);
            } catch (error) {
              Alert.alert('Error', 'Failed to remove website');
            }
          },
        },
      ]
    );
  };

  if (isLoading || !blockingSettings) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollContent}
      >
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color="#1F2937" />
          </TouchableOpacity>
          <Text style={styles.title}>Distraction Blocking</Text>
          <View style={styles.placeholder} />
        </View>
      </LinearGradient>

      {/* Main Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>General Settings</Text>
        
        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Shield size={24} color="#10B981" />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Enable Blocking</Text>
                <Text style={styles.settingDescription}>
                  Block distracting apps and websites during focus sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.isEnabled}
              onValueChange={handleToggleBlocking}
              trackColor={{ false: '#E5E7EB', true: '#10B981' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Zap size={24} color="#F59E0B" />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Strict Mode</Text>
                <Text style={styles.settingDescription}>
                  Prevent disabling blocking during active focus sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.strictMode}
              onValueChange={handleToggleStrictMode}
              trackColor={{ false: '#E5E7EB', true: '#F59E0B' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Clock size={24} color="#6366F1" />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Auto-Start with Timer</Text>
                <Text style={styles.settingDescription}>
                  Automatically enable blocking when any timer is started
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.autoStartWithTimer}
              onValueChange={(value) => updateBlockingSettings({ autoStartWithTimer: value })}
              trackColor={{ false: '#E5E7EB', true: '#6366F1' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        <View style={styles.settingCard}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Settings size={24} color="#8B5CF6" />
              <View style={styles.settingText}>
                <Text style={styles.settingLabel}>Manual Activation</Text>
                <Text style={styles.settingDescription}>
                  Allow manual activation of blocking outside of timer sessions
                </Text>
              </View>
            </View>
            <Switch
              value={blockingSettings.allowManualActivation}
              onValueChange={(value) => updateBlockingSettings({ allowManualActivation: value })}
              trackColor={{ false: '#E5E7EB', true: '#8B5CF6' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>
      </View>

      {/* Quick Add Suggestions */}
      {suggestedApps.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Quick Add Popular Apps</Text>
            <Text style={styles.sectionSubtitle}>Tap to add commonly blocked apps</Text>
          </View>

          <View style={styles.suggestedAppsGrid}>
            {suggestedApps.slice(0, 6).map((app, index) => {
              const isAlreadyBlocked = blockedApps.some(blockedApp =>
                blockedApp.packageName === app.packageName
              );

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.suggestedAppCard,
                    isAlreadyBlocked && styles.suggestedAppCardBlocked
                  ]}
                  onPress={() => {
                    if (!isAlreadyBlocked) {
                      addBlockedApp(app.name, app.packageName);
                    }
                  }}
                  disabled={isAlreadyBlocked}
                >
                  <Text style={styles.suggestedAppIcon}>
                    {app.category === 'social' ? '📱' :
                     app.category === 'entertainment' ? '🎬' :
                     app.category === 'browser' ? '🌐' :
                     app.category === 'messaging' ? '💬' : '📱'}
                  </Text>
                  <Text style={[
                    styles.suggestedAppName,
                    isAlreadyBlocked && styles.suggestedAppNameBlocked
                  ]}>
                    {app.name}
                  </Text>
                  {isAlreadyBlocked && (
                    <View style={styles.blockedBadge}>
                      <Check size={12} color="#FFFFFF" />
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      )}

      {/* Blocked Apps */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Blocked Apps</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              setShowAddApp(true);
              getPopularAppSuggestions();
            }}
          >
            <Plus size={20} color="#6B46C1" />
          </TouchableOpacity>
        </View>

        {/* Platform Info */}
        <Text style={styles.platformInfo}>
          {Platform.OS === 'ios' ? '📱 iOS: Showing common apps' :
           Platform.OS === 'android' ? '🤖 Android: Showing common apps' :
           '💻 Web: Add apps manually'}
        </Text>

        {showAddApp && (
          <View style={styles.addForm}>
            <View style={styles.searchContainer}>
              <Search size={20} color="#9CA3AF" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search for apps (e.g., Instagram, Facebook)"
                value={appSearchQuery}
                onChangeText={setAppSearchQuery}
                autoCapitalize="words"
                onFocus={() => {
                  if (suggestedApps.length > 0) {
                    setShowAppSuggestions(true);
                  }
                }}
              />
            </View>

            {/* Loading State */}
            {isLoadingApps && (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading installed apps...</Text>
              </View>
            )}

            {/* Error State */}
            {appLoadError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{appLoadError}</Text>
              </View>
            )}

            {/* Quick Category Buttons */}
            {!appSearchQuery && (
              <View style={styles.categoryContainer}>
                <Text style={styles.categoryTitle}>Quick Categories:</Text>
                <View style={styles.categoryButtons}>
                  <TouchableOpacity
                    style={styles.categoryButton}
                    onPress={async () => {
                      const apps = await installedAppsService.getAppSuggestionsByCategory('social');
                      setSuggestedApps(apps.slice(0, 8));
                      setShowAppSuggestions(true);
                    }}
                  >
                    <Text style={styles.categoryButtonText}>📱 Social</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.categoryButton}
                    onPress={async () => {
                      const apps = await installedAppsService.getAppSuggestionsByCategory('entertainment');
                      setSuggestedApps(apps.slice(0, 8));
                      setShowAppSuggestions(true);
                    }}
                  >
                    <Text style={styles.categoryButtonText}>🎮 Entertainment</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.categoryButton}
                    onPress={async () => {
                      const apps = await installedAppsService.getAppSuggestionsByCategory('productivity');
                      setSuggestedApps(apps.slice(0, 8));
                      setShowAppSuggestions(true);
                    }}
                  >
                    <Text style={styles.categoryButtonText}>💼 Work</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* App Suggestions */}
            {showAppSuggestions && suggestedApps.length > 0 && (
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsTitle}>Suggested Apps:</Text>
                <FlatList
                  data={suggestedApps}
                  keyExtractor={(item) => item.packageName}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.suggestionItem}
                      onPress={() => handleAddApp(item)}
                    >
                      <Text style={styles.suggestionIcon}>{item.icon}</Text>
                      <View style={styles.suggestionInfo}>
                        <Text style={styles.suggestionName}>{item.appName}</Text>
                        <Text style={styles.suggestionPackage}>{item.packageName}</Text>
                      </View>
                      <Plus size={16} color="#10B981" />
                    </TouchableOpacity>
                  )}
                  style={styles.suggestionsList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                />
              </View>
            )}

            {/* Manual Entry Fallback */}
            <TextInput
              style={styles.textInput}
              placeholder="Or enter app name manually"
              value={newAppName}
              onChangeText={setNewAppName}
              autoCapitalize="words"
            />

            <View style={styles.addFormButtons}>
              <TouchableOpacity
                style={[styles.formButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddApp(false);
                  setNewAppName('');
                  setAppSearchQuery('');
                  setSuggestedApps([]);
                  setShowAppSuggestions(false);
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.formButton, styles.addFormButton]}
                onPress={() => handleAddApp()}
              >
                <Text style={styles.addButtonText}>Add App</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {blockedApps.map((app) => (
          <View key={app.id} style={styles.itemCard}>
            <View style={styles.itemInfo}>
              <Text style={styles.itemIcon}>{app.icon}</Text>
              <View style={styles.itemText}>
                <Text style={styles.itemName}>{app.name}</Text>
                <Text style={styles.itemCategory}>{app.category}</Text>
              </View>
            </View>
            <View style={styles.itemActions}>
              <Switch
                value={app.isBlocked}
                onValueChange={(value) => handleToggleAppBlocking(app.id, value)}
                trackColor={{ false: '#E5E7EB', true: '#EF4444' }}
                thumbColor="#FFFFFF"
                style={styles.itemSwitch}
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveApp(app.id, app.name)}
              >
                <Trash2 size={18} color="#EF4444" />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {/* Blocked Websites */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Blocked Websites</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddWebsite(true)}
          >
            <Plus size={20} color="#6B46C1" />
          </TouchableOpacity>
        </View>

        {showAddWebsite && (
          <View style={styles.addForm}>
            <Text style={styles.formHelpText}>
              Add websites you want to block during focus sessions
            </Text>
            <TextInput
              style={styles.textInput}
              placeholder="Website name (e.g., Facebook, Instagram, YouTube)"
              value={newWebsiteName}
              onChangeText={setNewWebsiteName}
              autoCapitalize="words"
            />
            <TextInput
              style={styles.textInput}
              placeholder="Website URL (e.g., facebook.com, instagram.com)"
              value={newWebsiteUrl}
              onChangeText={setNewWebsiteUrl}
              autoCapitalize="none"
              keyboardType="url"
              autoCorrect={false}
            />
            <Text style={styles.formHelpText}>
              💡 Tip: Enter just the domain (e.g., "youtube.com") to block the entire site
            </Text>
            <View style={styles.addFormButtons}>
              <TouchableOpacity
                style={[styles.formButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddWebsite(false);
                  setNewWebsiteName('');
                  setNewWebsiteUrl('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.formButton, styles.addFormButton]}
                onPress={handleAddWebsite}
              >
                <Text style={styles.addButtonText}>Add Website</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {blockedWebsites.map((website) => (
          <View key={website.id} style={styles.itemCard}>
            <View style={styles.itemInfo}>
              <Globe size={24} color="#6B7280" />
              <View style={styles.itemText}>
                <Text style={styles.itemName}>{website.name}</Text>
                <Text style={styles.itemCategory}>{website.domain}</Text>
              </View>
            </View>
            <View style={styles.itemActions}>
              <Switch
                value={website.isBlocked}
                onValueChange={(value) => handleToggleWebsiteBlocking(website.id, value)}
                trackColor={{ false: '#E5E7EB', true: '#EF4444' }}
                thumbColor="#FFFFFF"
                style={styles.itemSwitch}
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveWebsite(website.id, website.name)}
              >
                <Trash2 size={18} color="#EF4444" />
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  placeholder: {
    width: 40,
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B46C1',
  },
  settingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 16,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  addForm: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
    backgroundColor: '#F9FAFB',
    marginBottom: 12,
  },
  addFormButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  formButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  addFormButton: {
    backgroundColor: '#6B46C1',
  },
  addButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  itemCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  itemText: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 2,
  },
  itemCategory: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  itemSwitch: {
    transform: [{ scaleX: 0.9 }, { scaleY: 0.9 }],
  },
  removeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FEF2F2',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: '#FFFFFF',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  suggestionsContainer: {
    marginBottom: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  suggestionsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 12,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  suggestionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    marginBottom: 2,
  },
  suggestionPackage: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  formHelpText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  loadingContainer: {
    padding: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  loadingTextSmall: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#DC2626',
  },
  platformInfo: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginBottom: 16,
    textAlign: 'center',
  },
  categoryContainer: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  suggestedAppsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 16,
  },
  suggestedAppCard: {
    width: '30%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  suggestedAppCardBlocked: {
    backgroundColor: '#F3F4F6',
    borderColor: '#10B981',
    opacity: 0.7,
  },
  suggestedAppIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  suggestedAppName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    textAlign: 'center',
  },
  suggestedAppNameBlocked: {
    color: '#6B7280',
  },
  blockedBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#10B981',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
