import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';

import { LinearGradient } from 'expo-linear-gradient';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Clock,
  Timer as TimerIcon,
  Settings as SettingsIcon,
  Shield,
  Volume2,
  Smartphone,
  Zap,
  Target,
} from 'lucide-react-native';

import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useTimer } from '@/hooks/useTimer';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { useRouter } from 'expo-router';
import BlockingNotifications, { NotificationData } from '@/components/BlockingNotifications';
import DistractionBlockingDemo from '@/components/DistractionBlockingDemo';
import { notificationService } from '@/services/notificationService';

const { width } = Dimensions.get('window');

interface SoundOption {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export default function TimerScreen() {
  const router = useRouter();
  const {
    isRunning,
    time,
    mode,
    currentSubject,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTotalTimeToday,
  } = useTimer();

  // Use distraction blocking hook
  const {
    isBlockingEnabled,
    stats,
    recentlyBlockedApps,
    setFocusContext,
    recordDistractionAttempt,
    isLoading: isBlockingLoading,
  } = useDistractionBlocking();

  // Local state
  const [showSettings, setShowSettings] = useState(false);
  const [showDemo, setShowDemo] = useState(false);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Focus sounds state
  const [soundOptions, setSoundOptions] = useState<SoundOption[]>([
    { id: '1', name: 'Rain', icon: '🌧️', active: false },
    { id: '2', name: 'Forest', icon: '🌲', active: false },
    { id: '3', name: 'Ocean', icon: '🌊', active: false },
    { id: '4', name: 'Coffee Shop', icon: '☕', active: false },
  ]);

  // Update distraction blocking context when timer state changes
  useEffect(() => {
    if (isRunning) {
      // Automatically enable focus mode when any timer is running
      if (mode === 'pomodoro') {
        setFocusContext(pomodoroPhase === 'work' ? 'focus' : 'break');
      } else {
        // For stopwatch, always use focus context
        setFocusContext('focus');
      }
    } else {
      setFocusContext('normal');
    }
  }, [isRunning, mode, pomodoroPhase, setFocusContext]);

  // Listen for distraction attempts to show in-app notifications
  useEffect(() => {
    if (!isRunning) return;

    const { mobileAppMonitoringService } = require('@/services/mobileAppMonitoringService');

    const unsubscribe = mobileAppMonitoringService.onDistractionAttempt((attempt: any) => {
      if (attempt.blocked) {
        addNotification({
          type: 'warning',
          title: '🚫 App Blocked',
          message: `${attempt.appName} is blocked during focus time. Stay focused!`,
          duration: 4000,
        });
      }
    });

    return unsubscribe;
  }, [isRunning]);

  const addNotification = (notification: Omit<NotificationData, 'id'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
    };
    setNotifications(prev => [...prev, newNotification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const toggleSound = (soundId: string) => {
    setSoundOptions(prev =>
      prev.map(sound =>
        sound.id === soundId
          ? { ...sound, active: !sound.active }
          : { ...sound, active: false }
      )
    );
  };

  const handleModeSwitch = (newMode: 'stopwatch' | 'pomodoro') => {
    if (isRunning) {
      stopTimer();
    }
    switchMode(newMode);
  };

  const handleStartTimer = () => {
    startTimer();

    // Show focus notification when timer starts (automatic focus mode)
    addNotification({
      type: 'info',
      title: '🎯 Focus Mode Activated',
      message: 'Timer started! Distracting apps are now blocked.',
      duration: 3000,
    });
  };

  const getTimerProgress = () => {
    if (mode === 'stopwatch') return 0;

    const totalTime = pomodoroPhase === 'work' ? 25 * 60 : 5 * 60; // Simplified for demo
    return ((totalTime - time) / totalTime) * 100;
  };

  const getPhaseText = () => {
    if (mode === 'stopwatch') {
      return isRunning ? 'Focus Time' : 'Stopwatch';
    }
    return pomodoroPhase === 'work' ? 'Focus Time' : 'Break Time';
  };

  const getPhaseColor = (): [string, string] => {
    if (mode === 'stopwatch') {
      return isRunning ? ['#6B46C1', '#7C3AED'] : ['#6366F1', '#8B5CF6'];
    }
    return pomodoroPhase === 'work' ? ['#6B46C1', '#7C3AED'] : ['#10B981', '#34D399'];
  };

  const getPhaseIcon = () => {
    if (mode === 'stopwatch') {
      return isRunning ? '🎯' : '⏱️';
    }
    return pomodoroPhase === 'work' ? '🎯' : '☕';
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.headerGradient}>
          <View style={styles.header}>
            <IsotopeLogo size="medium" />
            <View style={styles.headerActions}>
              {isRunning && (
                <TouchableOpacity
                  style={styles.demoButton}
                  onPress={() => setShowDemo(!showDemo)}
                >
                  <Text style={styles.demoButtonText}>
                    {showDemo ? 'Hide Demo' : 'Test Blocking'}
                  </Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.settingsButton}
                onPress={() => setShowSettings(!showSettings)}
              >
                <SettingsIcon size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Mode Selector */}
        <View style={styles.modeSection}>
          <View style={styles.modeSelector}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'stopwatch' && styles.modeButtonActive,
              ]}
              onPress={() => handleModeSwitch('stopwatch')}
            >
              <Clock size={20} color={mode === 'stopwatch' ? '#FFFFFF' : '#6B7280'} />
              <Text
                style={[
                  styles.modeText,
                  mode === 'stopwatch' && styles.modeTextActive,
                ]}
              >
                Stopwatch
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'pomodoro' && styles.modeButtonActive,
              ]}
              onPress={() => handleModeSwitch('pomodoro')}
            >
              <TimerIcon size={20} color={mode === 'pomodoro' ? '#FFFFFF' : '#6B7280'} />
              <Text
                style={[
                  styles.modeText,
                  mode === 'pomodoro' && styles.modeTextActive,
                ]}
              >
                Pomodoro
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Subject Picker */}
        <View style={styles.subjectSection}>
          <SubjectPicker
            selectedSubject={currentSubject}
            onSelectSubject={setCurrentSubject}
          />
        </View>

        {/* Focus Stats - Only show when timer is running */}
        {isRunning && (
          <View style={styles.statsSection}>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Zap size={20} color="#6B46C1" />
                <Text style={styles.statValue}>
                  {mode === 'pomodoro' ? pomodoroSession : '1'}
                </Text>
                <Text style={styles.statLabel}>Session</Text>
              </View>

              <View style={styles.statCard}>
                <Target size={20} color="#10B981" />
                <Text style={styles.statValue}>
                  {mode === 'pomodoro' ? '4' : '∞'}
                </Text>
                <Text style={styles.statLabel}>Goal</Text>
              </View>

              <View style={styles.statCard}>
                <Smartphone size={20} color="#EF4444" />
                <Text style={styles.statValue}>{stats.blockedToday}</Text>
                <Text style={styles.statLabel}>Blocked</Text>
              </View>
            </View>
          </View>
        )}

        {/* Main Timer */}
        <View style={styles.timerSection}>
          <View style={styles.timerContainer}>
            <LinearGradient
              colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
              style={styles.timerCard}
            >
              <View style={styles.timerHeader}>
                <View style={styles.phaseIndicator}>
                  <LinearGradient
                    colors={getPhaseColor()}
                    style={styles.phaseGradient}
                  >
                    <Text style={styles.phaseIcon}>
                      {getPhaseIcon()}
                    </Text>
                  </LinearGradient>
                </View>
                <View style={styles.timerInfo}>
                  <Text style={styles.timerLabel}>{getPhaseText()}</Text>
                  {mode === 'pomodoro' && (
                    <Text style={styles.cycleText}>
                      Session {pomodoroSession}
                    </Text>
                  )}
                  {isRunning && (
                    <Text style={styles.focusIndicator}>
                      🛡️ Focus mode active
                    </Text>
                  )}
                </View>
              </View>

              <View style={styles.timerDisplay}>
                <Text style={styles.timerText}>{formatTime(time)}</Text>

                {mode === 'pomodoro' && (
                  <View style={styles.progressContainer}>
                    <View style={styles.progressTrack}>
                      <LinearGradient
                        colors={getPhaseColor()}
                        style={[
                          styles.progressFill,
                          { width: `${getTimerProgress()}%` }
                        ]}
                      />
                    </View>
                    <Text style={styles.progressText}>
                      {Math.round(getTimerProgress())}% Complete
                    </Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </View>

          {/* Timer Controls */}
          <View style={styles.timerControls}>
            <TouchableOpacity style={styles.controlButton} onPress={resetTimer}>
              <RotateCcw size={24} color="#6366F1" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.playButton} onPress={isRunning ? pauseTimer : handleStartTimer}>
              <LinearGradient
                colors={['#6366F1', '#8B5CF6']}
                style={styles.playGradient}
              >
                {isRunning ? (
                  <Pause size={32} color="#FFFFFF" />
                ) : (
                  <Play size={32} color="#FFFFFF" />
                )}
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.controlButton} onPress={stopTimer}>
              <Square size={24} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Focus Sounds - Show when timer is running */}
        {isRunning && (
          <View style={styles.soundsSection}>
            <Text style={styles.sectionTitle}>Focus Sounds</Text>
            <View style={styles.soundGrid}>
              {soundOptions.map((sound) => (
                <TouchableOpacity
                  key={sound.id}
                  style={[
                    styles.soundCard,
                    sound.active && styles.soundCardActive,
                  ]}
                  onPress={() => toggleSound(sound.id)}
                >
                  <Text style={styles.soundIcon}>{sound.icon}</Text>
                  <Text
                    style={[
                      styles.soundName,
                      sound.active && styles.soundNameActive,
                    ]}
                  >
                    {sound.name}
                  </Text>
                  {sound.active && (
                    <View style={styles.activeBadge}>
                      <Volume2 size={16} color="#FFFFFF" />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Distraction Blocking - Always show for settings access */}
        <View style={styles.blockingSection}>
          <View style={styles.blockingSectionHeader}>
            <Text style={styles.sectionTitle}>Distraction Blocking</Text>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => router.push('/distraction-settings')}
            >
              <SettingsIcon size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.blockingCard}>
            <View style={styles.blockingHeader}>
              <Shield size={24} color={isRunning && isBlockingEnabled ? "#10B981" : "#6B7280"} />
              <Text style={styles.blockingTitle}>
                {isRunning ? 'Active Blocking' : 'Auto-Focus Mode'}
              </Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: isRunning && isBlockingEnabled ? "#10B981" : "#6B7280" }
              ]}>
                <Text style={styles.statusText}>
                  {isRunning && isBlockingEnabled ? "ACTIVE" : "READY"}
                </Text>
              </View>
            </View>

            <Text style={styles.blockingDescription}>
              {isRunning && isBlockingEnabled
                ? "Distracting apps are currently blocked. Focus mode is active!"
                : isBlockingEnabled
                ? "Distraction blocking will automatically activate when you start any timer."
                : "Enable distraction blocking in settings to automatically block distracting apps during timer sessions."
              }
            </Text>

            {isRunning && recentlyBlockedApps.length > 0 && (
              <View style={styles.recentlyBlocked}>
                <Text style={styles.recentlyBlockedTitle}>Recently Blocked</Text>
                <View style={styles.recentlyBlockedList}>
                  {recentlyBlockedApps.slice(0, 3).map((app, index) => (
                    <View key={index} style={styles.blockedAppItem}>
                      <Text style={styles.blockedAppName}>{app.name}</Text>
                      <Text style={styles.blockedAppTime}>
                        {new Date(app.lastAttempt).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Today's Stats */}
        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>Today's Progress</Text>

          <View style={styles.progressCard}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{formatTime(getTotalTimeToday())}</Text>
              <Text style={styles.statLabel}>Total Time</Text>
            </View>

            <View style={styles.statDivider} />

            <View style={styles.statItem}>
              <Text style={[styles.statValue, { textAlign: 'center' }]}>
                {currentSubject ? currentSubject.name : 'No Subject'}
              </Text>
              <Text style={styles.statLabel}>Current Subject</Text>
            </View>
          </View>
        </View>

        {/* Quick Tips */}
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Tips</Text>

          <View style={styles.tipCard}>
            <Text style={styles.tipTitle}>
              {mode === 'stopwatch' ? '⏱️ Stopwatch Mode' : '🍅 Pomodoro Technique'}
            </Text>
            <Text style={styles.tipText}>
              {mode === 'stopwatch'
                ? 'Perfect for open-ended study sessions. Starting the timer automatically enables focus mode to block distractions.'
                : 'Work for 25 minutes, then take a 5-minute break. Focus mode automatically activates during work sessions to block distracting apps.'
              }
            </Text>
          </View>
        </View>

        {/* Demo Section */}
        {isRunning && isBlockingEnabled && (
          <DistractionBlockingDemo isVisible={showDemo} />
        )}

      </ScrollView>

      {/* Notifications */}
      <BlockingNotifications
        notifications={notifications}
        onDismiss={removeNotification}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  demoButton: {
    backgroundColor: '#6B46C1',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  demoButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  settingsButton: {
    width: 40,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modeSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  modeSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 16,
    gap: 8,
  },
  modeButtonActive: {
    backgroundColor: '#6B46C1',
  },
  modeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modeTextActive: {
    color: '#FFFFFF',
  },
  subjectSection: {
    paddingHorizontal: 24,
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 32,
    paddingHorizontal: 20,
  },
  timerContainer: {
    marginBottom: 32,
    width: '100%',
  },
  timerCard: {
    borderRadius: 32,
    padding: 32,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.15,
    shadowRadius: 32,
    elevation: 16,
  },
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 16,
  },
  phaseIndicator: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  phaseGradient: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phaseIcon: {
    fontSize: 24,
  },
  timerInfo: {
    flex: 1,
  },
  timerLabel: {
    fontSize: 18,
    fontFamily: 'Inter-Semibold',
    color: '#1F2937',
  },
  cycleText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  focusIndicator: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B46C1',
    marginTop: 4,
  },
  timerDisplay: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 64,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -2,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 12,
  },
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  timerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
    marginTop: 8,
  },
  controlButton: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 24,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
  playGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  soundsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  soundGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  soundCard: {
    width: (width - 72) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  soundCardActive: {
    borderColor: '#6B46C1',
    backgroundColor: '#F3F4F6',
  },
  soundIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  soundName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textAlign: 'center',
  },
  soundNameActive: {
    color: '#6B46C1',
  },
  activeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#6B46C1',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blockingSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  blockingSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  blockingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  blockingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  blockingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Semibold',
    color: '#1F2937',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  blockingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  recentlyBlocked: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 16,
  },
  recentlyBlockedTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Semibold',
    color: '#1F2937',
    marginBottom: 8,
  },
  recentlyBlockedList: {
    gap: 8,
  },
  blockedAppItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  blockedAppName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  blockedAppTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  progressSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  progressCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 20,
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },

});